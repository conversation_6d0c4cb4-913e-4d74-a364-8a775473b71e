import { MailerService } from '@nestjs-modules/mailer';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from './email.service.js';

// Mock MailerService to avoid loading native dependencies
const mockMailerService = {
  sendMail: jest.fn().mockResolvedValue(undefined),
};

// Mock the HandlebarsAdapter import to avoid Jest issues
jest.mock('@nestjs-modules/mailer/dist/adapters/handlebars.adapter.js', () => ({
  HandlebarsAdapter: jest.fn().mockImplementation((options) => ({
    compile: jest.fn(),
    render: jest.fn(),
    ...options,
  })),
}));

describe('EmailModule', () => {
  let module: TestingModule;
  let emailService: EmailService;
  let configService: ConfigService;

  // Mock the dynamic import for HandlebarsAdapter
  const mockHandlebarsAdapter = jest.fn().mockImplementation((options) => ({
    compile: jest.fn(),
    render: jest.fn(),
    ...options,
  }));

  beforeAll(() => {
    // Mock the dynamic import
    jest.doMock('@nestjs-modules/mailer/dist/adapters/handlebars.adapter.js', () => ({
      HandlebarsAdapter: mockHandlebarsAdapter,
    }));
  });

  beforeEach(async () => {
    // Clear all mocks
    jest.clearAllMocks();

    // Set up environment variables for testing
    process.env.NODE_ENV = 'test';
    process.env.SMTP_HOST = 'localhost';
    process.env.SMTP_PORT = '587';
    process.env.SMTP_SECURE = 'false';
    process.env.SMTP_USER = '<EMAIL>';
    process.env.SMTP_PASSWORD = 'testpass';
    process.env.EMAIL_FROM_NAME = 'Test App';
    process.env.SMTP_FROM = '<EMAIL>';
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe('Module Creation in Test Environment', () => {
    beforeEach(async () => {
      process.env.NODE_ENV = 'test';

      module = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            isGlobal: true,
            envFilePath: ['.env.test'],
          }),
        ],
        providers: [
          EmailService,
          {
            provide: MailerService,
            useValue: mockMailerService,
          },
        ],
        exports: [EmailService],
      }).compile();

      emailService = module.get<EmailService>(EmailService);
      configService = module.get<ConfigService>(ConfigService);
    });

    it('should be defined', () => {
      expect(module).toBeDefined();
    });

    it('should provide EmailService', () => {
      expect(emailService).toBeDefined();
      expect(emailService).toBeInstanceOf(EmailService);
    });

    it('should export EmailService', () => {
      const exportedService = module.get<EmailService>(EmailService);
      expect(exportedService).toBeDefined();
      expect(exportedService).toBe(emailService);
    });

    it('should configure MailerService with correct settings', () => {
      const mailerService = module.get(MailerService);
      expect(mailerService).toBeDefined();
      expect(mailerService).toBe(mockMailerService);
    });

    it('should inject ConfigService into EmailService', () => {
      expect(configService).toBeDefined();
      expect(configService).toBeInstanceOf(ConfigService);
    });
  });

  describe('Module Directory Resolution', () => {
    let originalImportMeta: any;
    let originalDirname: any;

    beforeEach(() => {
      // Save original values
      originalImportMeta = global.eval;
      originalDirname = (global as any).__dirname;
    });

    afterEach(() => {
      // Restore original values
      global.eval = originalImportMeta;
      (global as any).__dirname = originalDirname;
    });

    it('should handle ES module path resolution', () => {
      // Mock eval to return import.meta with url
      global.eval = jest.fn().mockReturnValue({
        url: 'file:///app/src/email/email.module.js'
      });

      // Re-require the module to test the path resolution
      jest.resetModules();
      const { EmailModule } = require('./email.module.js');
      expect(EmailModule).toBeDefined();
    });

    it('should fall back to CommonJS __dirname', () => {
      // Mock eval to throw (simulating ES module failure)
      global.eval = jest.fn().mockImplementation(() => {
        throw new Error('import.meta not available');
      });

      // Set __dirname
      (global as any).__dirname = '/app/src/email';

      // Re-require the module to test the fallback
      jest.resetModules();
      const { EmailModule } = require('./email.module.js');
      expect(EmailModule).toBeDefined();
    });

    it('should fall back to process.cwd() when __dirname is undefined', () => {
      // Mock eval to throw
      global.eval = jest.fn().mockImplementation(() => {
        throw new Error('import.meta not available');
      });

      // Remove __dirname
      delete (global as any).__dirname;

      // Mock process.cwd
      const originalCwd = process.cwd;
      process.cwd = jest.fn().mockReturnValue('/app');

      try {
        // Re-require the module to test the final fallback
        jest.resetModules();
        const { EmailModule } = require('./email.module.js');
        expect(EmailModule).toBeDefined();
      } finally {
        // Restore process.cwd
        process.cwd = originalCwd;
      }
    });
  });

  describe('MailerModule Configuration', () => {
    let mockConfigService: jest.Mocked<ConfigService>;

    beforeEach(() => {
      mockConfigService = {
        get: jest.fn(),
      } as any;
    });

    it('should configure SMTP transport in test environment', async () => {
      mockConfigService.get.mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'NODE_ENV': 'test',
          'SMTP_HOST': 'localhost',
          'SMTP_PORT': '587',
          'SMTP_SECURE': 'false',
          'SMTP_USER': '<EMAIL>',
          'SMTP_PASSWORD': 'testpass',
          'EMAIL_FROM_NAME': 'Test App',
          'SMTP_FROM': '<EMAIL>',
        };
        return config[key] || defaultValue;
      });

      // Test the factory function logic
      const isTest = mockConfigService.get('NODE_ENV') === 'test';
      expect(isTest).toBe(true);

      const config = {
        transport: {
          host: mockConfigService.get('SMTP_HOST'),
          port: parseInt(mockConfigService.get('SMTP_PORT', '587'), 10),
          secure: mockConfigService.get('SMTP_SECURE') === 'true',
          auth: {
            user: mockConfigService.get('SMTP_USER'),
            pass: mockConfigService.get('SMTP_PASSWORD'),
          },
          ignoreTLS: mockConfigService.get('NODE_ENV') === 'development',
          requireTLS: mockConfigService.get('NODE_ENV') === 'production',
        },
        defaults: {
          from: `"${mockConfigService.get('EMAIL_FROM_NAME', 'RSGlider')}" <${mockConfigService.get('SMTP_FROM', '<EMAIL>')}>`,
        },
        preview: mockConfigService.get('NODE_ENV') === 'development',
      };

      expect(config.transport.host).toBe('localhost');
      expect(config.transport.port).toBe(587);
      expect(config.transport.secure).toBe(false);
      expect(config.transport.ignoreTLS).toBe(false);
      expect(config.transport.requireTLS).toBe(false);
      expect(config.defaults.from).toBe('"Test App" <<EMAIL>>');
      expect(config.preview).toBe(false);
    });

    it('should configure for development environment', async () => {
      mockConfigService.get.mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'NODE_ENV': 'development',
          'SMTP_HOST': 'localhost',
          'SMTP_PORT': '587',
          'SMTP_SECURE': 'false',
          'SMTP_USER': '<EMAIL>',
          'SMTP_PASSWORD': 'devpass',
          'EMAIL_FROM_NAME': 'Dev App',
          'SMTP_FROM': '<EMAIL>',
        };
        return config[key] || defaultValue;
      });

      const config = {
        transport: {
          ignoreTLS: mockConfigService.get('NODE_ENV') === 'development',
          requireTLS: mockConfigService.get('NODE_ENV') === 'production',
        },
        preview: mockConfigService.get('NODE_ENV') === 'development',
      };

      expect(config.transport.ignoreTLS).toBe(true);
      expect(config.transport.requireTLS).toBe(false);
      expect(config.preview).toBe(true);
    });

    it('should configure for production environment', async () => {
      mockConfigService.get.mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'NODE_ENV': 'production',
          'SMTP_HOST': 'smtp.example.com',
          'SMTP_PORT': '465',
          'SMTP_SECURE': 'true',
          'SMTP_USER': '<EMAIL>',
          'SMTP_PASSWORD': 'prodpass',
          'EMAIL_FROM_NAME': 'Production App',
          'SMTP_FROM': '<EMAIL>',
        };
        return config[key] || defaultValue;
      });

      const config = {
        transport: {
          secure: mockConfigService.get('SMTP_SECURE') === 'true',
          ignoreTLS: mockConfigService.get('NODE_ENV') === 'development',
          requireTLS: mockConfigService.get('NODE_ENV') === 'production',
        },
        preview: mockConfigService.get('NODE_ENV') === 'development',
      };

      expect(config.transport.secure).toBe(true);
      expect(config.transport.ignoreTLS).toBe(false);
      expect(config.transport.requireTLS).toBe(true);
      expect(config.preview).toBe(false);
    });
  });

  describe('Handlebars Configuration', () => {
    it('should skip HandlebarsAdapter in test environment', () => {
      // In test environment, HandlebarsAdapter should not be loaded
      // This is tested by the module creation succeeding without template config
      expect(true).toBe(true);
    });

    it('should test handlebars helper functions', () => {
      // Test the helper functions that would be configured

      // formatDate helper
      const formatDate = function (date: any) {
        return new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }).format(new Date(date));
      };

      const testDate = new Date('2023-12-25');
      expect(formatDate(testDate)).toBe('December 25, 2023');

      // ifEquals helper
      const ifEquals = function (arg1: any, arg2: any, options: any) {
        return (arg1 === arg2) ? options.fn(this) : options.inverse(this);
      };

      const mockOptions = {
        fn: jest.fn().mockReturnValue('equal'),
        inverse: jest.fn().mockReturnValue('not equal'),
      };

      expect(ifEquals('test', 'test', mockOptions)).toBe('equal');
      expect(ifEquals('test', 'other', mockOptions)).toBe('not equal');

      // url helper
      const originalEnv = process.env.EMAIL_BASE_URL;
      process.env.EMAIL_BASE_URL = 'https://example.com';

      const url = function (path: any) {
        const baseUrl = process.env.EMAIL_BASE_URL || 'http://localhost:3000';
        return `${baseUrl}${path}`;
      };

      expect(url('/test')).toBe('https://example.com/test');

      // Test fallback
      delete process.env.EMAIL_BASE_URL;
      expect(url('/test')).toBe('http://localhost:3000/test');

      // Restore
      if (originalEnv) {
        process.env.EMAIL_BASE_URL = originalEnv;
      }
    });
  });
});
