import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseService } from '../common/services/database.service.js';
import { UsersService } from '../users/users.service.js';
import { EmailManagementService } from './email-management.service.js';
import { EmailService } from './email.service.js';

describe('EmailManagementService', () => {
  let service: EmailManagementService;
  let databaseService: jest.Mocked<DatabaseService>;
  let emailService: jest.Mocked<EmailService>;
  let usersService: jest.Mocked<UsersService>;

  const mockDatabaseService = {
    db: {
      insert: jest.fn().mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn(),
        }),
      }),
      select: jest.fn().mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            orderBy: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                offset: jest.fn(),
              }),
            }),
            limit: jest.fn(),
          }),
          orderBy: jest.fn().mockReturnValue({
            limit: jest.fn(),
          }),
          limit: jest.fn(),
        }),
      }),
      update: jest.fn().mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            returning: jest.fn(),
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          returning: jest.fn(),
        }),
      }),
    },
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  const mockUsersService = {
    findUsersByRole: jest.fn(),
    findAllUsers: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailManagementService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    service = module.get<EmailManagementService>(EmailManagementService);
    databaseService = module.get(DatabaseService);
    emailService = module.get(EmailService);
    usersService = module.get(UsersService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('createTemplate', () => {
    it('should create a new email template', async () => {
      const createDto = {
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        category: 'notification',
        isActive: true,
      };

      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockTemplate]);

      const result = await service.createTemplate(createDto);

      expect(result.id).toBe('template-123');
      expect(result.name).toBe('Test Template');
      expect(result.subject).toBe('Test Subject');
      expect(result.htmlContent).toBe('<h1>Test</h1>');
      expect(result.textContent).toBe('Test');
      expect(result.category).toBe('notification');
      expect(result.isActive).toBe(true);

      expect(mockDatabaseService.db.insert).toHaveBeenCalled();
    });

    it('should create template with variables', async () => {
      const createDto = {
        name: 'Template with Variables',
        subject: 'Hello {{name}}',
        htmlContent: '<h1>Hello {{name}}</h1>',
        textContent: 'Hello {{name}}',
        variables: ['name', 'email'],
        category: 'welcome',
      };

      const mockTemplate = {
        id: 'template-456',
        name: 'Template with Variables',
        subject: 'Hello {{name}}',
        htmlContent: '<h1>Hello {{name}}</h1>',
        textContent: 'Hello {{name}}',
        variables: '["name","email"]',
        category: 'welcome',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockTemplate]);

      const result = await service.createTemplate(createDto);

      expect(result.variables).toEqual(['name', 'email']);
      expect(mockDatabaseService.db.insert).toHaveBeenCalled();
    });
  });

  describe('getTemplates', () => {
    it('should return paginated templates', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Template 1',
          subject: 'Subject 1',
          htmlContent: '<h1>Content 1</h1>',
          textContent: 'Content 1',
          variables: null,
          category: 'notification',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockCountResult = [{ count: 1 }];

      // Mock the chained query methods
      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockTemplates),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getTemplates({});

      expect(result.data).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });

    it('should filter templates by search term', async () => {
      const queryDto = { search: 'welcome', page: 1, limit: 5 };

      const mockTemplates = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockTemplates),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getTemplates(queryDto);

      expect(result.data).toHaveLength(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(5);
    });
  });

  describe('getTemplate', () => {
    it('should return a template by id', async () => {
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: '["name"]',
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const result = await service.getTemplate('template-123');

      expect(result.id).toBe('template-123');
      expect(result.variables).toEqual(['name']);
    });

    it('should throw NotFoundException when template not found', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      await expect(service.getTemplate('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateTemplate', () => {
    it('should update an existing template', async () => {
      const updateDto = {
        name: 'Updated Template',
        subject: 'Updated Subject',
      };

      const mockUpdatedTemplate = {
        id: 'template-123',
        name: 'Updated Template',
        subject: 'Updated Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.update().set().where().returning.mockResolvedValue([mockUpdatedTemplate]);

      const result = await service.updateTemplate('template-123', updateDto);

      expect(result.name).toBe('Updated Template');
      expect(result.subject).toBe('Updated Subject');
    });

    it('should throw NotFoundException when updating non-existent template', async () => {
      const updateDto = { name: 'Updated' };

      mockDatabaseService.db.update().set().where().returning.mockResolvedValue([]);

      await expect(service.updateTemplate('nonexistent', updateDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteTemplate', () => {
    it('should delete a template', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([{ id: 'template-123' }]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.delete().where().returning.mockResolvedValue([{ id: 'template-123' }]);

      await expect(service.deleteTemplate('template-123')).resolves.not.toThrow();
    });

    it('should throw NotFoundException when deleting non-existent template', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      // Mock delete to return empty array (no rows affected)
      mockDatabaseService.db.delete().where().returning.mockResolvedValue([]);

      await expect(service.deleteTemplate('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('createCampaign', () => {
    it('should create a new email campaign', async () => {
      const createDto = {
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all_users',
      };

      // Mock template exists
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const mockCampaign = {
        id: 'campaign-123',
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all_users',
        recipientCriteria: null,
        status: 'draft',
        scheduledAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockCampaign]);

      const result = await service.createCampaign(createDto);

      expect(result.id).toBe('campaign-123');
      expect(result.name).toBe('Test Campaign');
      expect(result.status).toBe('draft');
    });

    it('should create scheduled campaign', async () => {
      const scheduledDate = new Date(Date.now() + 86400000); // Tomorrow
      const createDto = {
        name: 'Scheduled Campaign',
        templateId: 'template-123',
        targetAudience: 'premium_users',
        scheduledAt: scheduledDate.toISOString(),
      };

      // Mock template exists
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const mockCampaign = {
        id: 'campaign-456',
        name: 'Scheduled Campaign',
        templateId: 'template-123',
        targetAudience: 'premium_users',
        recipientCriteria: null,
        status: 'scheduled',
        scheduledAt: scheduledDate,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockCampaign]);

      const result = await service.createCampaign(createDto);

      expect(result.status).toBe('scheduled');
      expect(result.scheduledAt).toEqual(scheduledDate);
    });
  });

  describe('sendEmail', () => {
    it('should send email with template', async () => {
      const sendDto = {
        templateId: 'template-123',
        recipients: ['<EMAIL>'],
        variables: { name: 'John Doe' },
      };

      // Mock template
      const mockTemplate = {
        id: 'template-123',
        name: 'Welcome Template',
        subject: 'Welcome {{name}}',
        htmlContent: '<h1>Welcome {{name}}</h1>',
        textContent: 'Welcome {{name}}',
        variables: '["name"]',
        category: 'welcome',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockEmailService.sendEmail.mockResolvedValue(true);

      const mockLog = {
        id: 'log-123',
        templateId: 'template-123',
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Welcome John Doe',
        status: 'sent',
        sentAt: new Date(),
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(true);
      expect(result.logId).toBe('log-123');
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Welcome John Doe',
        template: 'custom',
        context: {
          content: '<h1>Welcome John Doe</h1>',
          textContent: 'Welcome John Doe',
          name: 'John Doe',
        },
      });
    });

    it('should send direct email without template', async () => {
      const sendDto = {
        recipients: ['<EMAIL>'],
        subject: 'Direct Email',
        htmlContent: '<h1>Direct Content</h1>',
        textContent: 'Direct Content',
      };

      mockEmailService.sendEmail.mockResolvedValue(true);

      const mockLog = {
        id: 'log-456',
        templateId: null,
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Direct Email',
        status: 'sent',
        sentAt: new Date(),
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(true);
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Direct Email',
        template: 'direct',
        context: {
          content: '<h1>Direct Content</h1>',
          textContent: 'Direct Content',
        },
      });
    });

    it('should handle email sending failure', async () => {
      const sendDto = {
        recipients: ['<EMAIL>'],
        subject: 'Test Email',
        htmlContent: '<h1>Test</h1>',
      };

      mockEmailService.sendEmail.mockResolvedValue(false);

      const mockLog = {
        id: 'log-789',
        templateId: null,
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Test Email',
        status: 'failed',
        errorMessage: 'Email sending failed',
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(false);
      expect(result.logId).toBe('log-789');
    });
  });

  describe('getAnalyticsSummary', () => {
    it('should call analytics methods', async () => {
      // Test that the method exists and can be called
      expect(service.getAnalyticsSummary).toBeDefined();
      expect(typeof service.getAnalyticsSummary).toBe('function');
    });
  });

  describe('getTemplateStats', () => {
    it('should call template stats methods', async () => {
      // Test that the method exists and can be called
      expect(service.getTemplateStats).toBeDefined();
      expect(typeof service.getTemplateStats).toBe('function');
    });
  });

  describe('getEmailLogs', () => {
    it('should return paginated email logs', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          templateId: 'template-123',
          campaignId: null,
          recipientEmail: '<EMAIL>',
          subject: 'Test Email',
          status: 'sent',
          sentAt: new Date(),
          createdAt: new Date(),
        },
      ];

      const mockCountResult = [{ count: 1 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs({});

      expect(result.data).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });
  });

  describe('utility methods', () => {
    it('should replace variables in text', () => {
      const text = 'Hello {{name}}, your email is {{email}}';
      const variables = { name: 'John', email: '<EMAIL>' };

      // Access private method through any cast
      const result = (service as any).replaceVariables(text, variables);

      expect(result).toBe('Hello John, your <NAME_EMAIL>');
    });

    it('should map template to response', () => {
      const template = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: '["name","email"]',
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = (service as any).mapTemplateToResponse(template);

      expect(result.variables).toEqual(['name', 'email']);
      expect(result.id).toBe('template-123');
    });

    it('should handle null variables in template mapping', () => {
      const template = {
        id: 'template-456',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = (service as any).mapTemplateToResponse(template);

      expect(result.variables).toBeUndefined();
    });
  });
});
