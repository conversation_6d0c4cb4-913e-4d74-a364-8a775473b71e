import { rolePermissions } from './role-permissions.schema';

describe('Role Permissions Schema', () => {
  it('should be defined', () => {
    expect(rolePermissions).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(rolePermissions[Symbol.for('drizzle:Name')]).toBe('role_permissions');
  });

  it('should have all required columns', () => {
    expect(rolePermissions.id).toBeDefined();
    expect(rolePermissions.roleId).toBeDefined();
    expect(rolePermissions.permissionId).toBeDefined();
    expect(rolePermissions.createdAt).toBeDefined();
  });

  it('should have id as primary key', () => {
    expect(rolePermissions.id.primary).toBe(true);
  });

  it('should have roleId as not null', () => {
    expect(rolePermissions.roleId.notNull).toBe(true);
  });

  it('should have permissionId as not null', () => {
    expect(rolePermissions.permissionId.notNull).toBe(true);
  });

  it('should have createdAt as not null', () => {
    expect(rolePermissions.createdAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(rolePermissions.id.dataType).toBe('string');
    expect(rolePermissions.roleId.dataType).toBe('string');
    expect(rolePermissions.permissionId.dataType).toBe('string');
    expect(rolePermissions.createdAt.dataType).toBe('date');
  });

  it('should have foreign key references defined in schema', () => {
    // Test that the schema imports and uses the referenced tables (lines 7-8)
    // This ensures the foreign key references are properly defined
    expect(() => {
      // Import the referenced schemas to ensure they're accessible
      const { roles } = require('./roles.schema');
      const { permissions } = require('./permissions.schema');

      expect(roles).toBeDefined();
      expect(permissions).toBeDefined();

      // Test that the schema can be used (this exercises the reference functions)
      expect(rolePermissions.roleId.dataType).toBe('string');
      expect(rolePermissions.permissionId.dataType).toBe('string');
    }).not.toThrow();
  });

  it('should support relationship queries', () => {
    // Test that all columns are accessible for queries
    expect(rolePermissions.id).toBeDefined();
    expect(rolePermissions.roleId).toBeDefined();
    expect(rolePermissions.permissionId).toBeDefined();
    expect(rolePermissions.createdAt).toBeDefined();
  });

  it('should maintain referential integrity', () => {
    // Test that foreign key columns reference the correct tables
    expect(() => {
      const { roles } = require('./roles.schema');
      const { permissions } = require('./permissions.schema');

      // These should not throw when the references are properly defined
      expect(roles.id).toBeDefined();
      expect(permissions.id).toBeDefined();
    }).not.toThrow();
  });

  it('should have correct table structure for many-to-many relationship', () => {
    // Verify this is a proper junction table for roles and permissions
    const columns = Object.keys(rolePermissions);
    expect(columns).toContain('id');
    expect(columns).toContain('roleId');
    expect(columns).toContain('permissionId');
    expect(columns).toContain('createdAt');

    // Should have exactly these core columns for a junction table
    const coreColumns = ['id', 'roleId', 'permissionId', 'createdAt'];
    coreColumns.forEach(col => {
      expect(columns).toContain(col);
    });
  });

  it('should support RBAC (Role-Based Access Control) patterns', () => {
    // Test that the schema supports typical RBAC operations
    expect(rolePermissions.roleId).toBeDefined();
    expect(rolePermissions.permissionId).toBeDefined();

    // Should be able to query by role to get permissions
    expect(rolePermissions.roleId.dataType).toBe('string');

    // Should be able to query by permission to get roles
    expect(rolePermissions.permissionId.dataType).toBe('string');
  });

  it('should have default values where expected', () => {
    expect(rolePermissions.id.hasDefault).toBe(true);
    expect(rolePermissions.createdAt.hasDefault).toBe(true);
  });
});
